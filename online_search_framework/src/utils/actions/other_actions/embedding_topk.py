# import requests
# from src.utils.actions.other_actions.topk_base import TopkBase
# from typing import Dict
# import numpy as np
#
#
# class <PERSON><PERSON><PERSON>(TopkBase):
#     def __init__(self, k):
#         super().__init__()
#         self.embedding_url = self.config["embedding_url"]
#         self.k = k
#
#     def ranking(self, query_with_link: dict):
#         formated_ = self.pre_format(query_with_link)
#         embd_q = []
#         embd_docs = []
#         for query, doc in formated_.items():
#             embd_docs.append(self.embed_documents(doc))
#             embd_q.append(self.embed_documents([query]))
#         topk_res = self.top_k_similar(embd_q, embd_docs, self.k)
#         return self.post_format(query_with_link.keys(), topk_res)
#
#     def cosine_similarity(self, A, B):
#         return np.dot(A, B) / (np.linalg.norm(A) * np.linalg.norm(B))
#
#     def top_k_similar(self, query_embds, doc_embds_list, k, threshold=0.60):
#         all_top_k_indices = []
#         for query_embd, doc_embds in zip(query_embds, doc_embds_list):
#             query_embd = query_embd[0]
#             similarities = [
#                 self.cosine_similarity(query_embd, doc_embd) for doc_embd in doc_embds
#             ]
#
#             filtered_indices = [
#                 i for i, sim in enumerate(similarities) if sim >= threshold
#             ]
#             sorted_indices = sorted(
#                 filtered_indices, key=lambda i: similarities[i], reverse=True
#             )
#             top_k_indices = sorted_indices[:k]
#             all_top_k_indices.append(top_k_indices)
#
#         return all_top_k_indices
#
#     def pre_format(self, query_with_link: Dict):
#         return {
#             category: [article_info["snippet"] for article_info in articles.values()]
#             for category, articles in query_with_link.items()
#         }
#
#     def post_format(self, query_keys, indices_list):
#         result_dict = {}
#         for key, indices in zip(query_keys, indices_list):
#             result_dict[key] = [str(index + 1) for index in indices]
#         return result_dict
#
#     def embed_documents(self, documents: list) -> list:
#
#         response = requests.post(
#             url=self.embedding_url,
#             json={"inputs": documents},
#             headers={"Content-Type": "application/json", "Accept": "application/json"},
#         )
#         if response.status_code == 200:
#             data = response.json()
#             embeddings = [item["emb"] for item in data["outputs"]]
#             if not all(isinstance(embedding, list) for embedding in embeddings):
#                 raise ValueError("Expected each embedding to be a list")
#         else:
#             print(f"Error: {response.status_code}, {response.json()}")
#             embeddings = []
#         return embeddings

import requests
from loguru import logger

from src.utils.actions.other_actions.topk_base import TopkBase
from typing import Dict
import numpy as np


class Piccolo(TopkBase):
    def __init__(self, k):
        super().__init__()
        self.embedding_url = self.config["embedding_url"]
        self.k = k

    def ranking(self, query_with_link: dict):
        formated_ = self.pre_format(query_with_link)
        embd_q = []
        embd_docs = []
        logger.info(f"formated_  :   {formated_}")
        for query, doc in formated_.items():
            embd_docs.append(self.embed_documents(doc))
            embd_q.append(self.embed_documents([query]))
        topk_res = self.top_k_similar(embd_q, embd_docs, self.k)
        logger.info(f"topk_res  :   {topk_res}")
        return self.post_format(query_with_link.keys(), topk_res)

    def cosine_similarity(self, A, B):
        result = np.dot(A, B) / (np.linalg.norm(A) * np.linalg.norm(B))
        return result

    def top_k_similar(self, query_embds, doc_embds_list, k, threshold=0.60):
        all_top_k_indices = []
        for query_embd, doc_embds in zip(query_embds, doc_embds_list):
            query_embd = query_embd[0]
            similarities = [
                self.cosine_similarity(query_embd, doc_embd) for doc_embd in doc_embds
            ]

            filtered_indices = [
                i for i, sim in enumerate(similarities) if sim >= threshold
            ]
            sorted_indices = sorted(
                filtered_indices, key=lambda i: similarities[i], reverse=True
            )
            top_k_indices = sorted_indices[:k]
            all_top_k_indices.append(top_k_indices)
        return all_top_k_indices

    def pre_format(self, query_with_link: Dict):
        return {
            category: [article_info["snippet"] for article_info in articles.values()]
            for category, articles in query_with_link.items()
        }

    def post_format(self, query_keys, indices_list):
        result_dict = {}
        for key, indices in zip(query_keys, indices_list):
            result_dict[key] = [str(index + 1) for index in indices]
        return result_dict

    def embed_documents(self, documents: list) -> list:
        response = requests.post(
            url=self.embedding_url,
            json={"inputs": documents},
            headers={"Content-Type": "application/json", "Accept": "application/json"},
            timeout=1
        )
        if response.status_code == 200:
            data = response.json()
            embeddings = data #[item["emb"] for item in data["outputs"]]
            if not all(isinstance(embedding, list) for embedding in embeddings):
                raise ValueError("Expected each embedding to be a list")
        else:
            print(f"Error: {response.status_code}, {response.json()}")
            embeddings = []
        return embeddings
