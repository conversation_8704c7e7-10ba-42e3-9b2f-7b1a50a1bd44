import requests
from typing import Dict
from src.utils.actions.other_actions.topk_base import TopkBase
from loguru import logger

class Rerank(TopkBase):
    def __init__(self, k):
        super().__init__()
        self.rerank_url = self.config["rerank_url"]
        self.k = k

    def ranking(self, query_with_link: dict):
        formated_ = self.pre_format(query_with_link)
        rerank_results = []
        for query, doc in formated_.items():
            rerank_results.append(self.rerank_documents(query, doc))
        topk_res = self.top_k_select(rerank_results, self.k)
        logger.debug(topk_res)
        return self.post_format(query_with_link.keys(), topk_res)

    def pre_format(self, query_with_link: Dict):
        return {
            category: [article_info["snippet"] for article_info in articles.values()]
            for category, articles in query_with_link.items()
        }

    def post_format(self, query_keys, indices_list):
        result_dict = {}
        for key, indices in zip(query_keys, indices_list):
            result_dict[key] = [str(index + 1) for index in indices]
        return result_dict

    def top_k_select(self, rerank_results, k, threshold=0.60):
        selected_indices = []

        for results in rerank_results:
            top_k_results = results[:k]
            logger.debug(top_k_results)
            filtered_results = [
                item["index"] for item in top_k_results if item["score"] >= threshold
            ]
            selected_indices.append(filtered_results)

        return selected_indices

    def rerank_documents(self, query: str, documents: list) -> list:

        response = requests.post(
            url=self.rerank_url,
            json={"query": query, "texts": documents},
            headers={"Content-Type": "application/json", "Accept": "application/json"},
        )
        if response.status_code == 200:
            data = response.json()
        else:
            logger.error(f"Error: {response.status_code}, {response.json()}")
            data = []
        return data
