# Default values for license.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
application:
  name: "cabin-online-search-service"
  version:
  sensitiveVariable:
    - name: POSTGRES_PASSWORD
      valueFrom:
        secretKeyRef:
          name: online-search-service-secret
          key: POSTGRES_PASSWORD
          optional: false
    - name: NOVA_AK
      valueFrom:
        secretKeyRef:
          name: online-search-service-secret
          key: <PERSON>VA_AK
          optional: false
    - name: NOVA_SK
      valueFrom:
        secretKeyRef:
          name: online-search-service-secret
          key: NOVA_SK
          optional: false
    - name: NOVA_APP_ID
      valueFrom:
        secretKeyRef:
          name: online-search-service-secret
          key: NOVA_APP_ID
          optional: false

  variables:
    - name: ACTIVE_ENV
      value: "prd"


replicaCount: 1

image:
  repository: registry.sensetime.com/senseautocameraservice/cabin-online-search-service
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag:

imagePullSecrets:
  - name: dc-7vbdv
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 8777

podProbe:
  startUp:
    disabled: true  #
    path: /sa_chat/web_search/v1/health
    port: 8777
    failureThreshold: 60
    periodSeconds: 10
    timeoutSeconds: 10
  readiness:
    disabled: true  #
    path: /sa_chat/web_search/v1/health
    port: 8777
    initialDelaySeconds: 10
    periodSeconds: 10
    failureThreshold: 2
    timeoutSeconds: 10
  liveness:
    disabled: true  #
    path: /sa_chat/web_search/v1/health
    port: 8777
    initialDelaySeconds: 10
    periodSeconds: 10
    failureThreshold: 2
    timeoutSeconds: 10

ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 250m
    memory: 1Gi

autoscaling:
  enabled: false
  minReplicas: 2
  maxReplicas: 4
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations:
  - key: "project"
    operator: "Equal"
    value: "autocloud"
    effect: "NoExecute"
affinity: {}
