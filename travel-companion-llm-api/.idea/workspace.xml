<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c95cc679-13e5-4e9d-bd2d-ee1527e1e571" name="Changes" comment="fix:&#10;1. ci deploy 2" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Jupyter Notebook" />
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="project/gac-agent-xcy-20250813" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2jMWdZagwdlkuVFMndL2ra5BUxi" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.agent (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.apis.executor&quot;: &quot;Run&quot;,
    &quot;Python.demo.executor&quot;: &quot;Run&quot;,
    &quot;Python.fetch_web_content.executor&quot;: &quot;Run&quot;,
    &quot;Python.llm_answer.executor&quot;: &quot;Run&quot;,
    &quot;Python.llm_apis.executor&quot;: &quot;Run&quot;,
    &quot;Python.main.executor&quot;: &quot;Run&quot;,
    &quot;Python.retrieval.executor&quot;: &quot;Run&quot;,
    &quot;Python.sensenova.executor&quot;: &quot;Run&quot;,
    &quot;Python.serpapi_service.executor&quot;: &quot;Run&quot;,
    &quot;Python.serper_service.executor&quot;: &quot;Run&quot;,
    &quot;Python.stage0_agent_intent.executor&quot;: &quot;Run&quot;,
    &quot;Python.stage1_web_search (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.stage2_agent_topk.executor&quot;: &quot;Run&quot;,
    &quot;Python.stage3_web_crawling.executor&quot;: &quot;Run&quot;,
    &quot;Python.stage_1_web_search.executor&quot;: &quot;Run&quot;,
    &quot;Python.stage_2_agent_topk.executor&quot;: &quot;Run&quot;,
    &quot;Python.test (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.test.executor&quot;: &quot;Run&quot;,
    &quot;Python.web_crawler.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;project/gac-agent&quot;,
    &quot;last_opened_file_path&quot;: &quot;/data/chenzhe/projects/travel-companion-llm-wrapper&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.keymap&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$" />
      <recent name="C:\wangziliang1\桌面\work\online_search_framework\src" />
      <recent name="C:\wangziliang1\桌面\work\web search\src" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\wangziliang1\桌面\work\online_search_framework\src\utiles" />
      <recent name="C:\wangziliang1\桌面\work\online_search_framework\src" />
      <recent name="C:\wangziliang1\桌面\work\web search\src" />
    </key>
  </component>
  <component name="RunManager" selected="Python.stage3_web_crawling">
    <configuration default="true" type="PythonConfigurationType" factoryName="Python">
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="demo" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="online_search_framework" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/src" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/src/demo.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="stage0_agent_intent" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="online_search_framework" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/src" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/src/stage0_agent_intent.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="stage1_web_search (1)" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="online_search_framework" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/src" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/src/stage1_web_search.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="stage2_agent_topk" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="online_search_framework" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/src" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/src/stage2_agent_topk.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="stage3_web_crawling" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="online_search_framework" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/src" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/src/stage3_web_crawling.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="Tox" factoryName="Tox">
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docs" factoryName="Docutils task">
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="" />
      <option name="docutils_input_file" value="" />
      <option name="docutils_output_file" value="" />
      <option name="docutils_params" value="" />
      <option name="docutils_task" value="" />
      <option name="docutils_open_in_browser" value="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docs" factoryName="Sphinx task">
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="" />
      <option name="docutils_input_file" value="" />
      <option name="docutils_output_file" value="" />
      <option name="docutils_params" value="" />
      <option name="docutils_task" value="" />
      <option name="docutils_open_in_browser" value="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="tests" factoryName="Doctests">
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="CLASS_NAME" value="" />
      <option name="METHOD_NAME" value="" />
      <option name="FOLDER_NAME" value="" />
      <option name="TEST_TYPE" value="TEST_SCRIPT" />
      <option name="PATTERN" value="" />
      <option name="USE_PATTERN" value="false" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.demo" />
      <item itemvalue="Python.stage0_agent_intent" />
      <item itemvalue="Python.stage1_web_search (1)" />
      <item itemvalue="Python.stage2_agent_topk" />
      <item itemvalue="Python.stage3_web_crawling" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.stage3_web_crawling" />
        <item itemvalue="Python.stage1_web_search (1)" />
        <item itemvalue="Python.demo" />
        <item itemvalue="Python.stage2_agent_topk" />
        <item itemvalue="Python.stage0_agent_intent" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-657d8234b839-64d779b69b7a-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26927.74" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c95cc679-13e5-4e9d-bd2d-ee1527e1e571" name="Changes" comment="" />
      <created>1721197516795</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1721197516795</updated>
      <workItem from="1721197518645" duration="8785000" />
      <workItem from="1721210586686" duration="11143000" />
      <workItem from="1721282245102" duration="6796000" />
      <workItem from="1721291896150" duration="2738000" />
      <workItem from="1721296896214" duration="15122000" />
      <workItem from="1721801190032" duration="495000" />
      <workItem from="1721801703320" duration="69646000" />
      <workItem from="1721987483783" duration="6273000" />
      <workItem from="1722039879412" duration="4000" />
      <workItem from="1722093001258" duration="6231000" />
      <workItem from="1753684640714" duration="1950000" />
      <workItem from="1753694613564" duration="34000" />
    </task>
    <task id="LOCAL-00001" summary="features:&#10;1. add dockerfile related&#10;2. use cabin qwen72B&#10;3. extend timeout for calssifier api call&#10;fix:&#10;1. demo&#10;2. api related requirements">
      <option name="closed" value="true" />
      <created>1725891165100</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1725891165100</updated>
    </task>
    <task id="LOCAL-00002" summary="features:&#10;1. ci related">
      <option name="closed" value="true" />
      <created>1725939056808</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1725939056808</updated>
    </task>
    <task id="LOCAL-00003" summary="fix:&#10;1. ci deploy">
      <option name="closed" value="true" />
      <created>1725940163679</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1725940163679</updated>
    </task>
    <task id="LOCAL-00004" summary="fix:&#10;1. ci deploy 2">
      <option name="closed" value="true" />
      <created>1725948681601</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1725948681601</updated>
    </task>
    <option name="localTasksCounter" value="5" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="add git ignore" />
    <MESSAGE value="features:&#10;1. add dockerfile related&#10;2. use cabin qwen72B&#10;3. extend timeout for calssifier api call&#10;fix:&#10;1. demo&#10;2. api related requirements" />
    <MESSAGE value="features:&#10;1. ci related" />
    <MESSAGE value="fix:&#10;1. ci deploy" />
    <MESSAGE value="fix:&#10;1. ci deploy 2" />
    <option name="LAST_COMMIT_MESSAGE" value="fix:&#10;1. ci deploy 2" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/fetch_web_content_py$stage_1_web_search.coverage" NAME="stage_1_web_search Coverage Results" MODIFIED="1721818320340" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/web_search$serpapi_service.coverage" NAME="serpapi_service Coverage Results" MODIFIED="1721282635869" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/fetch_web_content_py$demo.coverage" NAME="demo Coverage Results" MODIFIED="1721987499404" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/fetch_web_content_py$serpapi_service.coverage" NAME="serpapi_service Coverage Results" MODIFIED="1721808973832" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/web_search$main.coverage" NAME="main Coverage Results" MODIFIED="1721294436208" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/web_search$test.coverage" NAME="test Coverage Results" MODIFIED="1721209624923" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/fetch_web_content_py$llm_answer.coverage" NAME="llm_answer Coverage Results" MODIFIED="1721896858720" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src/utiles" />
    <SUITE FILE_PATH="coverage/web_search$test__1_.coverage" NAME="test (1) Coverage Results" MODIFIED="1721209636697" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/fetch_web_content_py$agent__1_.coverage" NAME="agent (1) Coverage Results" MODIFIED="1721870296070" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/fetch_web_content_py$stage_2_agent_topk.coverage" NAME="stage_2_agent_topk Coverage Results" MODIFIED="1721817962077" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/fetch_web_content_py$stage2_agent_topk.coverage" NAME="stage2_agent_topk Coverage Results" MODIFIED="1721975051565" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/web_search$retrieval.coverage" NAME="retrieval Coverage Results" MODIFIED="1721286181709" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/web_search$sensenova.coverage" NAME="sensenova Coverage Results" MODIFIED="1721208336502" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/core/clients" />
    <SUITE FILE_PATH="coverage/fetch_web_content_py$web_crawler.coverage" NAME="web_crawler Coverage Results" MODIFIED="1721872943796" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/fetch_web_content_py$main.coverage" NAME="main Coverage Results" MODIFIED="1721378603646" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/web_search$fetch_web_content.coverage" NAME="fetch_web_content Coverage Results" MODIFIED="1721293003919" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/fetch_web_content_py$stage0_agent_intent.coverage" NAME="stage0_agent_intent Coverage Results" MODIFIED="1721962223407" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/web_search$serper_service.coverage" NAME="serper_service Coverage Results" MODIFIED="1721292686461" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/fetch_web_content_py$stage1_web_search__1_.coverage" NAME="stage1_web_search (1) Coverage Results" MODIFIED="1722093022744" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/fetch_web_content_py$stage3_web_crawling.coverage" NAME="stage3_web_crawling Coverage Results" MODIFIED="1721871135176" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/fetch_web_content_py$llm_apis.coverage" NAME="llm_apis Coverage Results" MODIFIED="1721804866695" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
    <SUITE FILE_PATH="coverage/web_search$apis.coverage" NAME="apis Coverage Results" MODIFIED="1721211410476" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/core" />
    <SUITE FILE_PATH="coverage/web_search$llm_answer.coverage" NAME="llm_answer Coverage Results" MODIFIED="1721208387452" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src" />
  </component>
</project>