#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入兼容性 - 验证Python版本兼容性修复
"""

import sys
print(f"Python版本: {sys.version}")

try:
    # 测试main.py的导入
    print("🔍 测试main.py导入...")
    
    # 先测试typing导入
    from typing import Generator, AsyncGenerator
    from typing import Union, Optional
    print("✅ 基础typing导入成功")
    
    # 测试Self导入（兼容性修复）
    if sys.version_info >= (3, 11):
        from typing import Self
        print("✅ 从typing导入Self成功 (Python 3.11+)")
    else:
        try:
            from typing_extensions import Self
            print("✅ 从typing_extensions导入Self成功")
        except ImportError:
            from typing import TypeVar
            Self = TypeVar('Self')
            print("✅ 使用TypeVar作为Self的替代")
    
    # 测试其他关键导入
    import asyncio
    import json
    import logging
    import re
    import threading
    import uuid
    import time
    from argparse import ArgumentParser
    from concurrent.futures import ThreadPoolExecutor
    
    print("✅ 所有基础导入成功")
    
    # 测试FastAPI相关导入
    try:
        import requests
        import uvicorn
        from fastapi import FastAPI, Header
        from fastapi.responses import StreamingResponse
        from loguru import logger
        from pydantic import BaseModel
        print("✅ FastAPI相关导入成功")
    except ImportError as e:
        print(f"⚠️ FastAPI相关导入失败: {e}")
    
    # 尝试导入main模块（不执行）
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("main", "../main.py")
        if spec and spec.loader:
            main_module = importlib.util.module_from_spec(spec)
            # 不执行，只检查语法
            print("✅ main.py模块语法检查通过")
        else:
            print("⚠️ 无法加载main.py模块规范")
    except Exception as e:
        print(f"❌ main.py导入测试失败: {e}")
    
    print("\n🎉 导入兼容性测试完成！")
    
except Exception as e:
    print(f"❌ 导入测试失败: {e}")
    sys.exit(1)
