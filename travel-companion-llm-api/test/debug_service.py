#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务调试脚本 - 用于CI环境中调试服务启动问题
"""

import requests
import time
import sys
import subprocess
import socket
import j<PERSON>


def check_port_listening(port=8080):
    """检查端口是否在监听"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"检查端口时出错: {e}")
        return False


def check_processes():
    """检查Python进程"""
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        python_processes = [line for line in lines if 'python' in line.lower()]
        
        print("🔍 Python进程:")
        for proc in python_processes[:10]:  # 只显示前10个
            print(f"  {proc}")
        
        return len(python_processes) > 0
    except Exception as e:
        print(f"检查进程时出错: {e}")
        return False


def check_network():
    """检查网络端口"""
    try:
        result = subprocess.run(['netstat', '-tlnp'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        port_8080 = [line for line in lines if ':8080' in line]
        
        print("🌐 网络端口状态:")
        if port_8080:
            for line in port_8080:
                print(f"  {line}")
        else:
            print("  端口8080未监听")
        
        return len(port_8080) > 0
    except Exception as e:
        print(f"检查网络时出错: {e}")
        return False


def test_http_connection(url="http://127.0.0.1:8080"):
    """测试HTTP连接"""
    try:
        print(f"🔗 测试连接: {url}")
        response = requests.get(url, timeout=5)
        print(f"  状态码: {response.status_code}")
        print(f"  响应头: {dict(response.headers)}")
        return True
    except requests.exceptions.ConnectionError:
        print("  ❌ 连接被拒绝")
        return False
    except requests.exceptions.Timeout:
        print("  ❌ 连接超时")
        return False
    except Exception as e:
        print(f"  ❌ 连接异常: {e}")
        return False


def test_health_endpoint(url="http://127.0.0.1:8080/gac/travel-companion/health"):
    """测试健康检查端点"""
    try:
        print(f"🏥 测试健康检查: {url}")
        response = requests.get(url, timeout=10)
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"  响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                return data.get('status') == 'up'
            except json.JSONDecodeError:
                print(f"  响应内容: {response.text}")
                return False
        else:
            print(f"  响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"  ❌ 健康检查异常: {e}")
        return False


def show_service_log(log_file="../service.log"):
    """显示服务日志"""
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("📋 服务日志（最后50行）:")
        for line in lines[-50:]:
            print(f"  {line.rstrip()}")
        
        return True
    except FileNotFoundError:
        print(f"📋 服务日志文件未找到: {log_file}")
        return False
    except Exception as e:
        print(f"📋 读取服务日志时出错: {e}")
        return False


def main():
    """主函数"""
    print("🔍 服务调试信息")
    print("=" * 50)
    
    # 1. 检查进程
    print("\n1. 检查进程状态:")
    has_processes = check_processes()
    
    # 2. 检查网络端口
    print("\n2. 检查网络端口:")
    port_listening = check_network()
    
    # 3. 检查端口监听（使用socket）
    print("\n3. 检查端口监听（socket）:")
    socket_check = check_port_listening()
    print(f"  端口8080监听状态: {'✅ 是' if socket_check else '❌ 否'}")
    
    # 4. 测试HTTP连接
    print("\n4. 测试HTTP连接:")
    http_ok = test_http_connection()
    
    # 5. 测试健康检查
    print("\n5. 测试健康检查:")
    health_ok = test_health_endpoint()
    
    # 6. 显示服务日志
    print("\n6. 服务日志:")
    show_service_log()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 诊断总结:")
    print(f"  进程运行: {'✅' if has_processes else '❌'}")
    print(f"  端口监听: {'✅' if port_listening else '❌'}")
    print(f"  Socket连接: {'✅' if socket_check else '❌'}")
    print(f"  HTTP连接: {'✅' if http_ok else '❌'}")
    print(f"  健康检查: {'✅' if health_ok else '❌'}")
    
    # 建议
    print("\n💡 建议:")
    if not has_processes:
        print("  - 服务可能未启动或已崩溃")
    if not port_listening:
        print("  - 服务可能未绑定到8080端口")
    if not http_ok:
        print("  - HTTP服务可能未正常运行")
    if not health_ok:
        print("  - 健康检查端点可能有问题")
    
    if health_ok:
        print("  🎉 服务运行正常！")
        return 0
    else:
        print("  ⚠️ 服务存在问题")
        return 1


if __name__ == "__main__":
    sys.exit(main())
