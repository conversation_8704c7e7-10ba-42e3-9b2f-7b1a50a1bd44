#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速冒烟测试 - CI环境专用简化版本
只测试最关键的功能，减少CI执行时间
"""

import requests
import time
import json
import sys


def test_health_check(base_url):
    """测试健康检查"""
    print("🏥 测试健康检查...")
    try:
        url = f"{base_url}/gac/travel-companion/health"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "up":
                print("  ✅ 健康检查通过")
                return True
            else:
                print(f"  ❌ 健康状态异常: {data}")
                return False
        else:
            print(f"  ❌ 健康检查失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 健康检查异常: {e}")
        return False


def test_version_endpoint(base_url):
    """测试版本信息"""
    print("📋 测试版本信息...")
    try:
        url = f"{base_url}/gac/travel-companion/v2/version"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            version = data.get("version", "未知")
            print(f"  ✅ 版本信息: {version}")
            return True
        else:
            print(f"  ❌ 版本信息获取失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 版本信息异常: {e}")
        return False


def test_chat_basic(base_url):
    """测试基本聊天功能"""
    print("💬 测试基本聊天功能...")
    try:
        url = f"{base_url}/gac/travel-companion/v2/chat"
        headers = {
            "Content-Type": "application/json",
            "client-id": "1s3963nw8802M4O55yMuU6x37tOYQ682"
        }
        
        payload = {
            "location": {
                "lat": "23.02965",
                "lon": "113.49027"
            },
            "model_name": "SenseAutoChat-30B",
            "messages": [
                {
                    "role": "user",
                    "content": "广州有什么好吃的？"
                }
            ],
            "is_stream": False,
            "user_info": {
                "car_id": "demoCar82951",
                "user_id": "2",
                "category": ["natural_landscape_preference"]
            }
        }
        
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, dict) and data.get("success", False):
                poi_count = len(data.get("poi_list", []))
                print(f"  ✅ 聊天功能正常，POI数量: {poi_count}")
                return True
            else:
                print(f"  ❌ 聊天功能异常: {data.get('tips', '未知错误')}")
                return False
        else:
            print(f"  ❌ 聊天请求失败，状态码: {response.status_code}")
            print(f"  响应内容: {response.text[:200]}")
            return False
    except Exception as e:
        print(f"  ❌ 聊天功能异常: {e}")
        return False


def test_irrelevant_query(base_url):
    """测试无关问题识别"""
    print("🤖 测试无关问题识别...")
    try:
        url = f"{base_url}/gac/travel-companion/v2/chat"
        headers = {
            "Content-Type": "application/json",
            "client-id": "1s3963nw8802M4O55yMuU6x37tOYQ682"
        }
        
        payload = {
            "location": {
                "lat": "23.02965",
                "lon": "113.49027"
            },
            "model_name": "SenseAutoChat-30B",
            "messages": [
                {
                    "role": "user",
                    "content": "你好啊"
                }
            ],
            "is_stream": False,
            "user_info": {
                "car_id": "demoCar82951",
                "user_id": "2",
                "category": ["natural_landscape_preference"]
            }
        }
        
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            recommend_text = data.get("recommend", "")
            tips_text = data.get("tips", "")
            
            if "与POI不相关问题" in recommend_text or "与POI不相关问题" in tips_text:
                print("  ✅ 无关问题识别正常")
                return True
            else:
                print(f"  ⚠️ 无关问题识别可能有问题")
                print(f"  recommend: {recommend_text}")
                print(f"  tips: {tips_text}")
                return True  # 不作为关键失败项
        else:
            print(f"  ❌ 无关问题测试失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 无关问题测试异常: {e}")
        return False


def main():
    """主函数"""
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"
    
    print("🚀 快速冒烟测试")
    print(f"🎯 测试目标: {base_url}")
    print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    tests = [
        ("健康检查", test_health_check),
        ("版本信息", test_version_endpoint),
        ("基本聊天", test_chat_basic),
        ("无关问题识别", test_irrelevant_query),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}测试:")
        try:
            if test_func(base_url):
                passed += 1
            else:
                print(f"  ❌ {test_name}测试失败")
        except Exception as e:
            print(f"  ❌ {test_name}测试异常: {e}")
        
        print("-" * 30)
    
    print(f"\n📊 测试结果:")
    print(f"  总测试数: {total}")
    print(f"  通过测试: {passed}")
    print(f"  失败测试: {total - passed}")
    print(f"  成功率: {passed/total*100:.1f}%")
    
    if passed >= total * 0.75:  # 75%通过率即可
        print("🎉 快速冒烟测试通过！")
        return 0
    else:
        print("❌ 快速冒烟测试失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())
