#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Travel Companion LLM API 合并冒烟测试 001
测试服务地址: http://127.0.0.1:8080

测试内容：
1. 基础功能测试
2. 健康检查测试
3. 版本信息测试
4. 聊天接口测试
5. 流式响应测试
6. 映射接口测试
7. 参数边界测试
8. 性能测试（5轮）
9. 错误恢复测试
"""

import requests
import time
import json
import sys
from typing import List, Dict, Any
from statistics import mean, median


class TravelCompanionSmokeTest:
    """Travel Companion LLM API 冒烟测试类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.health_url = f"{base_url}/gac/travel-companion/health"
        self.version_url = f"{base_url}/gac/travel-companion/v2/version"
        self.chat_url = f"{base_url}/gac/travel-companion/v2/chat"
        self.mapping_url = f"{base_url}/gac/travel-companion/v2/mapping"
        self.headers = {
            "Content-Type": "application/json",
            "client-id": "1s3963nw8802M4O55yMuU6x37tOYQ682"
        }
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, message: str, duration: float = 0):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "duration": duration,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {test_name} | {message} | {duration:.2f}s")

    def test_service_connectivity(self) -> bool:
        """测试1: 服务连通性测试"""
        test_name = "服务连通性测试"
        start_time = time.time()
        
        try:
            response = requests.get(self.base_url, timeout=25)
            duration = time.time() - start_time
            
            # FastAPI默认会返回404，但能连通说明服务正常
            if response.status_code in [200, 404]:
                self.log_test_result(test_name, True, f"服务连通正常 (状态码: {response.status_code})", duration)
                return True
            else:
                self.log_test_result(test_name, False, f"服务响应异常 (状态码: {response.status_code})", duration)
                return False
                
        except requests.exceptions.ConnectionError:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, "无法连接到服务", duration)
            return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"连接异常: {str(e)}", duration)
            return False
    
    def test_health_endpoint(self) -> bool:
        """测试2: 健康检查端点测试"""
        test_name = "健康检查端点测试"
        start_time = time.time()
        
        try:
            response = requests.get(self.health_url, timeout=25)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    health_data = response.json()
                    if health_data.get("status") == "up":
                        version = health_data.get("version", "未知")
                        self.log_test_result(test_name, True, f"健康检查通过 (版本: {version})", duration)
                        return True
                    else:
                        self.log_test_result(test_name, False, f"健康状态异常: {health_data}", duration)
                        return False
                except json.JSONDecodeError:
                    self.log_test_result(test_name, False, "健康检查响应格式错误", duration)
                    return False
            else:
                self.log_test_result(test_name, False, f"健康检查失败 (状态码: {response.status_code})", duration)
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"健康检查异常: {str(e)}", duration)
            return False

    def test_version_endpoint(self) -> bool:
        """测试3: 版本信息端点测试"""
        test_name = "版本信息端点测试"
        start_time = time.time()
        
        try:
            response = requests.get(self.version_url, timeout=25)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    version_data = response.json()
                    version = version_data.get("version", "未知")
                    self.log_test_result(test_name, True, f"版本信息获取成功 (版本: {version})", duration)
                    return True
                except json.JSONDecodeError:
                    self.log_test_result(test_name, False, "版本信息响应格式错误", duration)
                    return False
            else:
                self.log_test_result(test_name, False, f"版本信息获取失败 (状态码: {response.status_code})", duration)
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"版本信息获取异常: {str(e)}", duration)
            return False

    def test_chat_basic_request(self) -> bool:
        """测试4: 聊天接口基础请求测试"""
        test_name = "聊天接口基础请求测试"
        start_time = time.time()

        payload = {
            "location": {
                "lat": "23.02965",
                "lon": "113.49027"
            },
            "model_name": "SenseAutoChat-30B",
            "rewrite_model_name": "POI-rewrite-7B",
            "messages": [
                {
                    "role": "user",
                    "content": "广州有什么好吃的？"
                }
            ],
            "is_stream": False,
            "user_info": {
                "car_id": "demoCar82951",
                "user_id": "2",
                "category": ["natural_landscape_preference", "human_landscape_preference", "entertainment_landscape_preference", "travel_activity"]
            }
        }
        
        try:
            response = requests.post(
                self.chat_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            duration = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if isinstance(result, dict) and result.get("success", False):
                        poi_count = len(result.get("poi_list", []))
                        self.log_test_result(test_name, True, f"聊天请求成功 (POI数量: {poi_count})", duration)
                        return True
                    else:
                        self.log_test_result(test_name, False, f"聊天请求失败: {result.get('tips', '未知错误')}", duration)
                        return False
                except json.JSONDecodeError:
                    self.log_test_result(test_name, False, "聊天响应JSON格式错误", duration)
                    return False
            else:
                error_msg = response.text[:200] if response.text else "无错误信息"
                self.log_test_result(test_name, False, f"聊天请求失败 (状态码: {response.status_code}) - {error_msg}", duration)
                return False
                
        except requests.exceptions.Timeout:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, "聊天请求超时", duration)
            return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"聊天请求异常: {str(e)}", duration)
            return False

    def test_stream_response(self) -> bool:
        """测试5: 流式响应测试"""
        test_name = "流式响应测试"
        start_time = time.time()
        
        payload = {
            "location": {
                "lat": "23.02965",
                "lon": "113.49027"
            },
            "model_name": "SenseAutoChat-30B",
            "rewrite_model_name": "POI-rewrite-7B",
            "messages": [
                {
                    "role": "user",
                    "content": "深圳有什么特色景点？"
                }
            ],
            "is_stream": True,
            "user_info": {
                "car_id": "demoCar82951",
                "user_id": "2",
                "category": ["natural_landscape_preference", "human_landscape_preference", "entertainment_landscape_preference", "travel_activity"]
            }
        }
        
        try:
            response = requests.post(
                self.chat_url,
                headers=self.headers,
                json=payload,
                stream=True,
                timeout=30
            )
            
            if response.status_code == 200:
                chunks_received = 0
                
                # 读取流式数据
                for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                    if chunk:
                        chunks_received += 1
                        # 限制读取时间，避免无限等待
                        if time.time() - start_time > 25:
                            break
                
                duration = time.time() - start_time
                
                if chunks_received > 0:
                    self.log_test_result(test_name, True, f"流式响应正常 (接收{chunks_received}个数据块)", duration)
                    return True
                else:
                    self.log_test_result(test_name, False, "未接收到流式数据", duration)
                    return False
            else:
                duration = time.time() - start_time
                self.log_test_result(test_name, False, f"流式请求失败 (状态码: {response.status_code})", duration)
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"流式响应异常: {str(e)}", duration)
            return False

    def test_parameter_boundaries(self) -> bool:
        """测试7: 参数边界值测试"""
        test_name = "参数边界值测试"
        start_time = time.time()

        test_cases = [
            {"messages": [{"role": "user", "content": "北京"}], "desc": "最短查询"},
            {"messages": [{"role": "user", "content": "我想去北京玩请问哪里好玩，最好是休闲娱乐的地方，有什么推荐的景点和美食吗？"}], "desc": "长查询"},
            {"location": {"lat": "90.0", "lon": "180.0"}, "desc": "边界坐标"},
        ]

        base_payload = {
            "location": {
                "lat": "23.02965",
                "lon": "113.49027"
            },
            "model_name": "SenseAutoChat-30B",
            "messages": [
                {
                    "role": "user",
                    "content": "测试查询"
                }
            ],
            "is_stream": False,
            "user_info": {
                "car_id": "demoCar82951",
                "user_id": "2",
                "category": ["natural_landscape_preference"]
            }
        }

        successful_cases = 0

        for case in test_cases:
            try:
                payload = base_payload.copy()
                payload.update({k: v for k, v in case.items() if k != "desc"})

                response = requests.post(
                    self.chat_url,
                    headers=self.headers,
                    json=payload,
                    timeout=30
                )

                if response.status_code == 200:
                    successful_cases += 1
                    print(f"   ✓ {case['desc']}: 成功")
                else:
                    print(f"   ✗ {case['desc']}: 失败 (状态码: {response.status_code})")

            except Exception as e:
                print(f"   ✗ {case['desc']}: 异常 - {str(e)}")

        duration = time.time() - start_time

        if successful_cases >= len(test_cases) // 2:
            self.log_test_result(test_name, True, f"边界测试通过 ({successful_cases}/{len(test_cases)})", duration)
            return True
        else:
            self.log_test_result(test_name, False, f"边界测试失败 ({successful_cases}/{len(test_cases)})", duration)
            return False

    def extract_timing_info(self, response_data: Dict[str, Any]) -> Dict[str, float]:
        """从响应数据中提取时间信息"""
        timing_info = {}

        if isinstance(response_data, dict):
            # 从total_time字段中提取时间信息
            total_time = response_data.get("total_time", {})
            if isinstance(total_time, dict):
                # 提取各个时间节点
                time_fields = [
                    "t1-loc", "t2-mem", "t3-intent", "t4-src",
                    "t5-mft", "t6-acft", "Dify-TTFB"
                ]

                for field in time_fields:
                    if field in total_time:
                        try:
                            timing_info[field] = float(total_time[field])
                        except (ValueError, TypeError):
                            pass

        return timing_info

    def format_timing_summary(self, timing_info: Dict[str, float]) -> str:
        """格式化时间信息摘要"""
        if not timing_info:
            return ""

        # 时间字段对应的中文名称
        time_field_names = {
            "Dify-TTFB": "Dify智能体接口首字延迟",
            "t1-loc": "高德经纬度转换成城市",
            "t2-mem": "获取用户画像",
            "t3-intent": "识别调用高德时需要的信息",
            "t4-src": "高德poi搜索接口",
            "t5-mft": "大模型总结首字延迟",
            "t6-acft": "原子能力接口首字延迟"
        }

        summary_parts = []
        for field, duration in timing_info.items():
            if duration > 0.01:  # 只显示有意义的时间（>0.01s）
                chinese_name = time_field_names.get(field, field)
                summary_parts.append(f"{chinese_name}:{duration:.2f}s")

        return f"[{' '.join(summary_parts)}]" if summary_parts else ""

    def test_response_time_performance(self) -> bool:
        """测试8: 响应时间性能测试（5轮）"""
        test_name = "响应时间性能测试"
        start_time = time.time()

        # 多样化的测试查询
        test_queries = [
            "3公里内有什么推荐的饭店吗",
            "帮我找个人均价格不超过100的饭店",
            "想找个地方买东西喝",
            "推荐一家健身房",
            "不想去美宜佳"
        ]

        response_times = []
        successful_requests = 0
        test_rounds = 5
        all_timing_data = []

        print(f"   执行 {test_rounds} 轮性能测试...")

        for i in range(test_rounds):
            query = test_queries[i % len(test_queries)]

            payload = {
                "location": {
                    "lat": "23.02965",
                    "lon": "113.49027"
                },
                "model_name": "SenseAutoChat-30B",
                "rewrite_model_name": "POI-rewrite-7B",
                "messages": [
                    {
                        "role": "user",
                        "content": query
                    }
                ],
                "is_stream": False,
                "user_info": {
                    "car_id": "demoCar82951",
                    "user_id": "2",
                    "category": ["natural_landscape_preference", "human_landscape_preference", "entertainment_landscape_preference", "travel_activity"]
                }
            }

            try:
                req_start = time.time()
                response = requests.post(
                    self.chat_url,
                    headers=self.headers,
                    json=payload,
                    timeout=30
                )
                req_duration = time.time() - req_start

                if response.status_code == 200:
                    response_times.append(req_duration)
                    successful_requests += 1

                    # 解析响应中的时间信息
                    try:
                        result = response.json()
                        timing_info = self.extract_timing_info(result)
                        all_timing_data.append(timing_info)
                        poi_count = len(result.get("poi_list", []))
                        timing_summary = self.format_timing_summary(timing_info)
                        print(f"   第{i+1}轮: {req_duration:.2f}s (POI数量: {poi_count}) {timing_summary}")
                    except Exception as e:
                        print(f"   第{i+1}轮: {req_duration:.2f}s (响应解析失败: {str(e)})")
                else:
                    print(f"   第{i+1}轮: 失败 (状态码: {response.status_code})")

            except Exception as e:
                print(f"   第{i+1}轮: 异常 - {str(e)}")

        duration = time.time() - start_time

        # 生成性能统计报告
        if response_times and all_timing_data:
            avg_time = mean(response_times)
            median_time = median(response_times)
            max_time = max(response_times)
            min_time = min(response_times)
            success_rate = successful_requests / test_rounds

            print(f"\n   📊 性能测试统计报告:")
            print(f"   ================================")
            print(f"   总体响应时间:")
            print(f"     平均: {avg_time:.2f}s, 中位数: {median_time:.2f}s")
            print(f"     最快: {min_time:.2f}s, 最慢: {max_time:.2f}s")
            print(f"     成功率: {success_rate:.1%}")

            # 计算各模块的平均耗时
            time_field_names = {
                "Dify-TTFB": "Dify智能体接口首字延迟",
                "t1-loc": "高德经纬度转换成城市",
                "t2-mem": "获取用户画像",
                "t3-intent": "识别调用高德时需要的信息",
                "t4-src": "高德poi搜索接口",
                "t5-mft": "大模型总结首字延迟",
                "t6-acft": "原子能力接口首字延迟"
            }

            module_stats = {}
            for timing_info in all_timing_data:
                for field, duration in timing_info.items():
                    if field not in module_stats:
                        module_stats[field] = []
                    if duration > 0:
                        module_stats[field].append(duration)

            print(f"\n   各模块平均耗时:")
            sorted_modules = sorted(module_stats.items(),
                                  key=lambda x: mean(x[1]) if x[1] else 0, reverse=True)

            for field, durations in sorted_modules:
                if durations:
                    avg_duration = mean(durations)
                    chinese_name = time_field_names.get(field, field)
                    print(f"     {chinese_name:20s}: {avg_duration:6.2f}s (出现{len(durations)}次)")

            # 性能标准：平均响应时间 < 20秒，成功率 > 60%
            performance_ok = avg_time < 20.0 and success_rate > 0.6

            message = f"平均:{avg_time:.2f}s 中位数:{median_time:.2f}s 最大:{max_time:.2f}s 最小:{min_time:.2f}s 成功率:{success_rate:.1%}"
            self.log_test_result(test_name, performance_ok, message, duration)
            return performance_ok
        else:
            self.log_test_result(test_name, False, "所有请求都失败", duration)
            return False

    def test_error_recovery(self) -> bool:
        """测试9: 错误恢复能力测试"""
        test_name = "错误恢复能力测试"
        start_time = time.time()

        # 先发送一个错误请求
        invalid_payload = {
            "messages": [],  # 空消息
            "invalid_field": "test"
        }

        try:
            # 发送无效请求
            response = requests.post(
                self.chat_url,
                headers=self.headers,
                json=invalid_payload,
                timeout=30
            )
            print(f"   无效请求响应: {response.status_code}")
        except Exception as e:
            print(f"   无效请求异常: {str(e)}")

        # 短暂等待
        time.sleep(1)

        # 然后发送正常请求，测试服务是否能恢复
        valid_payload = {
            "location": {
                "lat": "23.02965",
                "lon": "113.49027"
            },
            "model_name": "SenseAutoChat-30B",
            "messages": [
                {
                    "role": "user",
                    "content": "天津有什么特色小吃？"
                }
            ],
            "is_stream": False,
            "user_info": {
                "car_id": "demoCar82951",
                "user_id": "2",
                "category": ["natural_landscape_preference", "human_landscape_preference", "entertainment_landscape_preference", "travel_activity"]
            }
        }

        recovery_successful = False

        try:
            response = requests.post(
                self.chat_url,
                headers=self.headers,
                json=valid_payload,
                timeout=30
            )

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get("success", False):
                        recovery_successful = True
                        print("   服务成功从错误中恢复")
                    else:
                        print(f"   恢复失败，服务返回错误: {result.get('tips', '未知错误')}")
                except json.JSONDecodeError:
                    print("   恢复失败，响应格式错误")
            else:
                print(f"   恢复失败，状态码: {response.status_code}")

        except Exception as e:
            print(f"   恢复测试异常: {str(e)}")

        duration = time.time() - start_time

        message = "服务能够从错误中恢复" if recovery_successful else "服务无法从错误中恢复"
        self.log_test_result(test_name, recovery_successful, message, duration)
        return recovery_successful

    def run_smoke_tests(self) -> bool:
        """运行所有冒烟测试"""
        print("=" * 80)
        print("🧪 Travel Companion LLM API 冒烟测试 001")
        print(f"🎯 测试目标: {self.base_url}")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

        # 执行测试序列
        tests = [
            self.test_service_connectivity,
            self.test_health_endpoint,
            self.test_version_endpoint,
            self.test_chat_basic_request,
            self.test_stream_response,
            self.test_parameter_boundaries,
            self.test_response_time_performance,
            self.test_error_recovery
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_func in tests:
            if test_func():
                passed_tests += 1
            print("-" * 80)

        # 输出测试总结
        success_rate = (passed_tests / total_tests) * 100
        overall_success = passed_tests == total_tests

        print("📊 测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   成功率: {success_rate:.1f}%")

        if overall_success:
            print("🎉 冒烟测试全部通过！服务功能正常")
        elif passed_tests >= total_tests * 0.75:
            print("✅ 大部分冒烟测试通过，服务基本正常")
        else:
            print("⚠️  多项冒烟测试失败，请检查服务状态")

        print("=" * 80)
        return overall_success


def main():
    """主函数"""
    # 可以通过命令行参数指定服务地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"

    smoke_test = TravelCompanionSmokeTest(base_url)
    success = smoke_test.run_smoke_tests()

    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
